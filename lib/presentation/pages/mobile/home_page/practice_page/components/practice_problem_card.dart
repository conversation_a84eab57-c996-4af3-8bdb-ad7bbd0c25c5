import 'package:flutter/material.dart';
import 'package:langda/backend/model/practice_problem_model.dart';
import 'package:langda/presentation/pages/mobile/constants.dart';

enum AnswerState {
  unanswered,
  correct,
  incorrect,
}

class PracticeProblemCard extends StatefulWidget {
  final PracticeProblem problem;
  final Function(String answer, bool isCorrect) onAnswerSelected;
  final bool isAnswered;
  final String? selectedAnswer;

  const PracticeProblemCard({
    super.key,
    required this.problem,
    required this.onAnswerSelected,
    this.isAnswered = false,
    this.selectedAnswer,
  });

  @override
  State<PracticeProblemCard> createState() => _PracticeProblemCardState();
}

class _PracticeProblemCardState extends State<PracticeProblemCard>
    with SingleTickerProviderStateMixin {
  String? _selectedAnswer;
  AnswerState _answerState = AnswerState.unanswered;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _selectedAnswer = widget.selectedAnswer;
    if (widget.isAnswered && _selectedAnswer != null) {
      _answerState = widget.problem.isCorrectAnswer(_selectedAnswer!)
          ? AnswerState.correct
          : AnswerState.incorrect;
    }

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _selectAnswer(String answer) {
    if (widget.isAnswered) return;

    setState(() {
      _selectedAnswer = answer;
      _answerState = widget.problem.isCorrectAnswer(answer)
          ? AnswerState.correct
          : AnswerState.incorrect;
    });

    // Trigger animation
    _animationController.forward().then((_) {
      _animationController.reverse();
    });

    // Call the callback
    widget.onAnswerSelected(answer, widget.problem.isCorrectAnswer(answer));
  }

  Color _getOptionColor(String option) {
    if (!widget.isAnswered || _selectedAnswer != option) {
      return Colors.white;
    }

    switch (_answerState) {
      case AnswerState.correct:
        return Colors.green.shade50;
      case AnswerState.incorrect:
        return Colors.red.shade50;
      case AnswerState.unanswered:
        return Colors.white;
    }
  }

  Color _getOptionBorderColor(String option) {
    if (!widget.isAnswered) {
      return _selectedAnswer == option ? LDColors.mainLime : LDColors.lightGrey;
    }

    if (_selectedAnswer == option) {
      switch (_answerState) {
        case AnswerState.correct:
          return Colors.green;
        case AnswerState.incorrect:
          return Colors.red;
        case AnswerState.unanswered:
          return LDColors.lightGrey;
      }
    }

    // Show correct answer if user selected wrong
    if (_answerState == AnswerState.incorrect &&
        widget.problem.isCorrectAnswer(option)) {
      return Colors.green;
    }

    return LDColors.lightGrey;
  }

  Widget _getOptionIcon(String option) {
    if (!widget.isAnswered || _selectedAnswer != option) {
      return const SizedBox.shrink();
    }

    switch (_answerState) {
      case AnswerState.correct:
        return Icon(
          Icons.check_circle,
          color: Colors.green,
          size: 20,
        );
      case AnswerState.incorrect:
        return Icon(
          Icons.cancel,
          color: Colors.red,
          size: 20,
        );
      case AnswerState.unanswered:
        return const SizedBox.shrink();
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
              border: Border.all(
                color: LDColors.lightGrey,
                width: 1,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Category badge (if available)
                if (widget.problem.category != null) ...[
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: LDColors.foundationLimeLight,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      widget.problem.category!,
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: LDColors.foundationLimeDark,
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                ],

                // Sentence with blank
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade50,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: LDColors.lightGrey,
                      width: 1,
                    ),
                  ),
                  child: Text(
                    widget.problem.sentenceWithBlank,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w500,
                      color: Colors.black87,
                      height: 1.4,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),

                const SizedBox(height: 20),

                // Answer options
                Text(
                  'Choose the correct word:',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 12),

                ...widget.problem.options.map((option) {
                  return Padding(
                    padding: const EdgeInsets.only(bottom: 8),
                    child: GestureDetector(
                      onTap: () => _selectAnswer(option),
                      child: Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: _getOptionColor(option),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: _getOptionBorderColor(option),
                            width: 2,
                          ),
                        ),
                        child: Row(
                          children: [
                            Expanded(
                              child: Text(
                                option,
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                  color: Colors.black87,
                                ),
                              ),
                            ),
                            _getOptionIcon(option),
                          ],
                        ),
                      ),
                    ),
                  );
                }).toList(),

                // Explanation (shown after answering)
                if (widget.isAnswered &&
                    widget.problem.explanation != null) ...[
                  const SizedBox(height: 16),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade50,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: Colors.blue.shade200,
                        width: 1,
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.lightbulb_outline,
                              color: Colors.blue.shade700,
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'Explanation',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                                color: Colors.blue.shade700,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          widget.problem.explanation!,
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.blue.shade800,
                            height: 1.4,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }
}
